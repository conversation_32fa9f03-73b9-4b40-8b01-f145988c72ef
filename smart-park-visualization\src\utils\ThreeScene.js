import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { CubeTextureLoader } from 'three/src/loaders/CubeTextureLoader.js'
import { GUIController } from './GUIController.js'
import { GUIControllerExtended } from './GUIControllerExtended.js'
import { LabelManager } from './LabelManager.js'
import { CSS3DLabelManager } from './CSS3DLabelManager.js'

import { FloorManager } from './FloorManager.js'
import { DynamicSkybox } from './DynamicSkybox.js'
import viewConfig from '../config/angleofview.json'

/**
 * Three.js 场景管理器
 * 负责创建和管理 3D 场景、相机、光照、渲染器等
 */
export class ThreeScene {
  constructor(container, enableGUI = true, guiType = 'basic') {
    this.container = container
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.loader = null
    this.model = null
    this.gui = null
    this.enableGUI = enableGUI
    this.guiType = guiType // 'basic' 或 'extended'

    // 天空盒相关属性
    this.skybox = null
    this.skyboxLoader = null
    this.originalBackground = null
    this.dynamicSkybox = null

    // 标签管理器
    this.labelManager = null
    this.css3DLabelManager = null



    // 楼层管理器
    this.floorManager = null

    // 昼夜模式相关
    this.isDayMode = false  // 默认夜晚模式
    this.dayLightSettings = {}  // 存储白天光照设置
    this.nightLightSettings = {}  // 存储夜晚光照设置

    // 动画循环控制
    this.animationId = null
    this.isAnimating = false

    // 绑定事件处理函数以确保正确的this上下文
    this.boundOnWindowResize = this.onWindowResize.bind(this)

    // 异步初始化
    this.init()
  }

  /**
   * 初始化 3D 场景
   */
  init() {
    this.createScene()
    this.createCamera()
    this.createRenderer()
    this.createLights()
    this.createControls()
    this.createLoader()
    this.createSkyboxLoader()
    this.createDynamicSkybox()
    this.createLabelManager()
    this.createCSS3DLabelManager()

    this.createFloorManager()
    this.setupEventListeners()

    // 初始化 GUI 控制器
    if (this.enableGUI) {
      this.initGUI()
    }

    // 设置默认夜晚模式
    this.toggleDayNightMode(false)

    // 异步加载静态天空盒（在昼夜模式设置之后）
    this.initSkybox()

    this.startAnimation()
  }

  /**
   * 异步初始化天空盒
   */
  async initSkybox() {
    console.log('开始初始化静态天空盒...')
    try {
      // 先确保动态天空盒被禁用
      if (this.dynamicSkybox) {
        console.log('禁用动态天空盒...')
        this.dynamicSkybox.disable()
      }

      console.log('加载静态天空盒贴图...')
      await this.loadSkybox()
      console.log('启用静态天空盒...')
      this.enableSkybox()
      console.log('✅ 静态天空盒已成功启用')
    } catch (error) {
      console.warn('❌ 静态天空盒加载失败，将使用动态天空盒:', error)
      // 如果静态天空盒加载失败，启用动态天空盒作为备选
      if (this.dynamicSkybox) {
        this.dynamicSkybox.enable()
      }
    }
  }

  /**
   * 创建场景
   */
  createScene() {
    this.scene = new THREE.Scene()
    this.originalBackground = new THREE.Color(0x2a2a2a) // 保存原始背景色
    this.scene.background = this.originalBackground.clone() // 稍微亮一些的背景
    // 默认不启用雾气，可通过GUI控制
    // this.scene.fog = new THREE.Fog(0x2a2a2a, 200, 1500)
  }

  /**
   * 创建相机
   */
  createCamera() {
    const aspect = this.container.clientWidth / this.container.clientHeight
    const config = viewConfig.defaultViewpoint
    this.camera = new THREE.PerspectiveCamera(config.camera.fov, aspect, config.camera.near, config.camera.far)
    this.camera.position.set(config.camera.position.x, config.camera.position.y, config.camera.position.z)
    this.camera.rotation.set(config.camera.rotation.x, config.camera.rotation.y, config.camera.rotation.z)
    this.camera.lookAt(config.controls.target.x, config.controls.target.y, config.controls.target.z)
  }

  /**
   * 创建渲染器
   */
  createRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true
    })

    // 强制使用窗口尺寸
    const width = window.innerWidth
    const height = window.innerHeight

    console.log('容器尺寸:', {
      containerWidth: this.container.clientWidth,
      containerHeight: this.container.clientHeight,
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
      finalWidth: width,
      finalHeight: height
    })

    this.renderer.setSize(width, height)
    this.renderer.setPixelRatio(window.devicePixelRatio)
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    this.renderer.outputColorSpace = THREE.SRGBColorSpace
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping
    this.renderer.toneMappingExposure = 2.0 // 增加曝光度，让模型更亮

    // 确保canvas样式正确
    this.renderer.domElement.style.width = '100vw'
    this.renderer.domElement.style.height = '100vh'
    this.renderer.domElement.style.display = 'block'
    this.renderer.domElement.style.position = 'absolute'
    this.renderer.domElement.style.top = '0'
    this.renderer.domElement.style.left = '0'

    this.container.appendChild(this.renderer.domElement)
  }

  /**
   * 创建光照
   */
  createLights() {
    // 增强环境光，提供基础照明
    const ambientLight = new THREE.AmbientLight(0x404040, 1.2)
    ambientLight.name = 'ambientLight'
    this.scene.add(ambientLight)

    // 主方向光 - 模拟太阳光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5)
    directionalLight.name = 'mainDirectionalLight'
    directionalLight.position.set(100, 100, 50)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    directionalLight.shadow.camera.near = 0.5
    directionalLight.shadow.camera.far = 500
    directionalLight.shadow.camera.left = -100
    directionalLight.shadow.camera.right = 100
    directionalLight.shadow.camera.top = 100
    directionalLight.shadow.camera.bottom = -100
    this.scene.add(directionalLight)

    // 补充光源 - 从另一个角度照亮
    const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.8)
    fillLight.name = 'fillLight'
    fillLight.position.set(-50, 50, -50)
    this.scene.add(fillLight)

    // 顶部光源
    const topLight = new THREE.DirectionalLight(0xffffff, 0.6)
    topLight.name = 'topLight'
    topLight.position.set(0, 100, 0)
    this.scene.add(topLight)

    // 侧面光源
    const sideLight1 = new THREE.DirectionalLight(0xffffff, 0.4)
    sideLight1.name = 'sideLight1'
    sideLight1.position.set(50, 0, 50)
    this.scene.add(sideLight1)

    const sideLight2 = new THREE.DirectionalLight(0xffffff, 0.4)
    sideLight2.name = 'sideLight2'
    sideLight2.position.set(-50, 0, -50)
    this.scene.add(sideLight2)

    // 点光源（模拟园区照明）
    const pointLight = new THREE.PointLight(0xffffff, 1.0, 300)
    pointLight.name = 'pointLight'
    pointLight.position.set(0, 50, 0)
    pointLight.castShadow = true
    this.scene.add(pointLight)

    // 存储白天光照设置
    this.saveDayLightSettings()
    // 初始化夜晚光照设置
    this.initNightLightSettings()
  }

  /**
   * 创建控制器
   */
  createControls() {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.controls.enableDamping = false  // 默认关闭阻尼
    this.controls.dampingFactor = 0.05
    this.controls.screenSpacePanning = true  // 启用屏幕空间平移，更自由的平移体验

    // 完全移除所有距离限制
    this.controls.minDistance = 0  // 完全无限制的最小距离
    this.controls.maxDistance = Infinity  // 完全无限制的最大距离

    // 完全移除所有角度限制
    this.controls.minAzimuthAngle = -Infinity  // 完全无限制的水平旋转
    this.controls.maxAzimuthAngle = Infinity   // 完全无限制的水平旋转
    this.controls.minPolarAngle = 0            // 完全无限制的垂直旋转
    this.controls.maxPolarAngle = Math.PI      // 完全无限制的垂直旋转

    // 启用所有控制功能，无任何限制
    this.controls.enablePan = true      // 启用平移
    this.controls.enableZoom = true     // 启用缩放
    this.controls.enableRotate = true   // 启用旋转

    // 设置控制速度，可以根据需要调整
    this.controls.panSpeed = 2.0        // 增加平移速度
    this.controls.rotateSpeed = 1.0     // 旋转速度
    this.controls.zoomSpeed = 1.0       // 缩放速度

    // 设置默认目标点
    const config = viewConfig.defaultViewpoint
    this.controls.target.set(config.controls.target.x, config.controls.target.y, config.controls.target.z)
    this.controls.update()
  }

  /**
   * 创建模型加载器
   */
  createLoader() {
    this.loader = new GLTFLoader()
  }

  /**
   * 创建天空盒加载器
   */
  createSkyboxLoader() {
    this.skyboxLoader = new CubeTextureLoader()
  }

  /**
   * 创建动态天空盒
   */
  createDynamicSkybox() {
    this.dynamicSkybox = new DynamicSkybox(this.scene, {
      skyRadius: 1000,
      sunPosition: { x: 0, y: 400, z: -800 },
      moonPosition: { x: 500, y: 300, z: 600 },
      cloudCount: 30,
      cloudSpeed: 0.0005,
      cloudHeight: 200,
      cloudSpread: 800,
      starCount: 200
    })
    // 默认不启用动态天空盒
    // this.dynamicSkybox.enable()
  }

  /**
   * 创建标签管理器
   */
  createLabelManager() {
    this.labelManager = new LabelManager(this)
  }

  /**
   * 创建CSS3D标签管理器
   */
  createCSS3DLabelManager() {
    this.css3DLabelManager = new CSS3DLabelManager(this)
  }



  /**
   * 创建楼层管理器
   */
  createFloorManager() {
    this.floorManager = new FloorManager(this)

    // 立即设置完全无限制的控制器状态
    if (this.floorManager && this.controls) {
      this.floorManager.disableControllerLimits()
    }
  }

  /**
   * 加载天空盒
   * @param {string} skyboxPath - 天空盒文件夹路径，默认为 'skybox/'
   * @returns {Promise} 加载Promise
   */
  loadSkybox(skyboxPath = './skybox/') {
    return new Promise((resolve, reject) => {
      // 定义立方体贴图的6个面文件名（按Three.js要求的顺序）
      const urls = [
        `${skyboxPath}px.jpg`,   // positive X
        `${skyboxPath}nx.jpg`,   // negative X
        `${skyboxPath}py.jpg`,   // positive Y
        `${skyboxPath}ny.jpg`,   // negative Y
        `${skyboxPath}pz.jpg`,   // positive Z
        `${skyboxPath}nz.jpg`    // negative Z
      ]

      this.skyboxLoader.load(
        urls,
        (cubeTexture) => {
          this.skybox = cubeTexture
          console.log('✅ 静态天空盒贴图加载成功')
          resolve(cubeTexture)
        },
        (progress) => {
          console.log('天空盒加载进度:', progress)
        },
        (error) => {
          console.error('❌ 天空盒加载失败:', error)
          reject(error)
        }
      )
    })
  }

  /**
   * 启用天空盒
   */
  enableSkybox() {
    if (this.skybox) {
      // 先禁用动态天空盒
      if (this.dynamicSkybox) {
        this.dynamicSkybox.disable()
      }
      // 设置静态天空盒为背景
      this.scene.background = this.skybox
      console.log('静态天空盒已启用')
    } else {
      console.warn('静态天空盒未加载，请先调用 loadSkybox()')
    }
  }

  /**
   * 禁用天空盒，恢复原始背景
   */
  disableSkybox() {
    // 恢复原始背景色
    this.scene.background = new THREE.Color('#2a2a2a')
    console.log('静态天空盒已禁用，恢复原始背景')
  }

  /**
   * 切换天空盒显示状态
   * @param {boolean} enabled - 是否启用天空盒
   */
  toggleSkybox(enabled) {
    if (enabled) {
      this.enableSkybox()
    } else {
      this.disableSkybox()
    }
  }

  /**
   * 切换动态天空盒显示状态
   * @param {boolean} enabled - 是否启用动态天空盒
   */
  toggleDynamicSkybox(enabled) {
    if (this.dynamicSkybox) {
      if (enabled) {
        // 启用动态天空盒时，清除静态天空盒背景
        this.scene.background = null
        this.dynamicSkybox.enable()
        console.log('动态天空盒已启用')
      } else {
        this.dynamicSkybox.disable()
        console.log('动态天空盒已禁用')
      }
    }
  }

  /**
   * 保存白天光照设置
   */
  saveDayLightSettings() {
    this.dayLightSettings = {
      background: '#2a2a2a',
      ambientLight: { color: 0x404040, intensity: 1.2 },
      mainDirectionalLight: { color: 0xffffff, intensity: 1.5 },
      fillLight: { color: 0x87ceeb, intensity: 0.8 },
      topLight: { color: 0xffffff, intensity: 0.6 },
      sideLight1: { color: 0xffffff, intensity: 0.4 },
      sideLight2: { color: 0xffffff, intensity: 0.4 },
      pointLight: { color: 0xffffff, intensity: 1.0 },
      toneMappingExposure: 2.0
    }
  }

  /**
   * 初始化夜晚光照设置
   */
  initNightLightSettings() {
    // 夜晚使用中性白色光源，减少蓝色调
    this.nightLightSettings = {
      background: '#1a1a1a', // 中性深灰背景
      ambientLight: { color: 0xf0f0f0, intensity: 1.0 }, // 中性白色环境光
      mainDirectionalLight: { color: 0xffffff, intensity: 1.2 }, // 纯白色主光源
      fillLight: { color: 0xf5f5f5, intensity: 0.6 }, // 浅白色补光
      topLight: { color: 0xffffff, intensity: 0.5 }, // 白色顶光
      sideLight1: { color: 0xfff8dc, intensity: 0.4 }, // 暖白色侧光1
      sideLight2: { color: 0xfff8dc, intensity: 0.4 }, // 暖白色侧光2
      pointLight: { color: 0xffffff, intensity: 0.8 }, // 白色点光源
      toneMappingExposure: 1.8 // 适中的曝光度
    }
  }

  /**
   * 切换昼夜模式
   * @param {boolean} isDayMode - 是否为白天模式
   */
  toggleDayNightMode(isDayMode = !this.isDayMode) {
    this.isDayMode = isDayMode

    const settings = isDayMode ? this.dayLightSettings : this.nightLightSettings

    // 只有在没有天空盒时才更新背景颜色
    if (!this.skybox || this.scene.background === null || this.scene.background instanceof THREE.Color) {
      this.scene.background = new THREE.Color(settings.background)
    }
    
    // 更新环境光
    const ambientLight = this.scene.getObjectByName('ambientLight')
    if (ambientLight) {
      ambientLight.color.setHex(settings.ambientLight.color)
      ambientLight.intensity = settings.ambientLight.intensity
    }
    
    // 更新主方向光
    const mainDirectionalLight = this.scene.getObjectByName('mainDirectionalLight')
    if (mainDirectionalLight) {
      mainDirectionalLight.color.setHex(settings.mainDirectionalLight.color)
      mainDirectionalLight.intensity = settings.mainDirectionalLight.intensity
    }
    
    // 更新补充光源
    const fillLight = this.scene.getObjectByName('fillLight')
    if (fillLight) {
      fillLight.color.setHex(settings.fillLight.color)
      fillLight.intensity = settings.fillLight.intensity
    }
    
    // 更新顶部光源
    const topLight = this.scene.getObjectByName('topLight')
    if (topLight) {
      topLight.color.setHex(settings.topLight.color)
      topLight.intensity = settings.topLight.intensity
    }
    
    // 更新侧面光源
    const sideLight1 = this.scene.getObjectByName('sideLight1')
    if (sideLight1) {
      sideLight1.color.setHex(settings.sideLight1.color)
      sideLight1.intensity = settings.sideLight1.intensity
    }
    
    const sideLight2 = this.scene.getObjectByName('sideLight2')
    if (sideLight2) {
      sideLight2.color.setHex(settings.sideLight2.color)
      sideLight2.intensity = settings.sideLight2.intensity
    }
    
    // 更新点光源
    const pointLight = this.scene.getObjectByName('pointLight')
    if (pointLight) {
      pointLight.color.setHex(settings.pointLight.color)
      pointLight.intensity = settings.pointLight.intensity
    }
    
    // 更新渲染器曝光度
    if (this.renderer) {
      this.renderer.toneMappingExposure = settings.toneMappingExposure
    }

    // 更新动态天空盒的夜晚模式
    if (this.dynamicSkybox) {
      this.dynamicSkybox.setNightMode(!isDayMode)
    }

    console.log(`已切换到${isDayMode ? '白天' : '夜晚'}模式`)
  }

  /**
   * 获取当前昼夜模式
   */
  getDayNightMode() {
    return this.isDayMode
  }

  /**
   * 加载 GLB 模型
   * @param {string} modelPath - 模型文件路径
   * @param {Function} onProgress - 加载进度回调
   * @param {Function} onError - 错误回调
   */
  loadModel(modelPath, onProgress = null, onError = null) {
    return new Promise((resolve, reject) => {
      this.loader.load(
        modelPath,
        (gltf) => {
          this.model = gltf.scene
          
          // 设置模型阴影
          this.model.traverse((child) => {
            if (child.isMesh) {
              child.castShadow = true
              child.receiveShadow = true
            }
          })

          // 计算模型边界盒，用于模型居中
          const box = new THREE.Box3().setFromObject(this.model)
          const center = box.getCenter(new THREE.Vector3())
          const size = box.getSize(new THREE.Vector3())

          // 将模型居中
          this.model.position.sub(center)

          // 使用默认视角，不自动调整相机位置
          const config = viewConfig.defaultViewpoint
          this.camera.position.set(config.camera.position.x, config.camera.position.y, config.camera.position.z)
          this.camera.rotation.set(config.camera.rotation.x, config.camera.rotation.y, config.camera.rotation.z)
          this.camera.lookAt(config.controls.target.x, config.controls.target.y, config.controls.target.z)
          this.controls.target.set(config.controls.target.x, config.controls.target.y, config.controls.target.z)

          // 强制设置缩放限制，确保可以缩小
          this.controls.minDistance = 0.001
          this.controls.maxDistance = Infinity
          this.controls.update()

          console.log('模型尺寸:', size)
          console.log('最大维度:', Math.max(size.x, size.y, size.z))
          console.log('使用默认相机位置:', this.camera.position)

          this.scene.add(this.model)

          // 将整体模型设置到楼层管理器，现在楼层控制直接作用于整体模型
          if (this.floorManager) {
            this.floorManager.setOverallModel(this.model)
          }

          resolve(gltf)
        },
        onProgress,
        (error) => {
          console.error('模型加载失败:', error)
          if (onError) onError(error)
          reject(error)
        }
      )
    })
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 防止重复绑定事件监听器
    this.removeEventListeners()
    window.addEventListener('resize', this.boundOnWindowResize)
  }

  /**
   * 移除事件监听器
   */
  removeEventListeners() {
    if (this.boundOnWindowResize) {
      window.removeEventListener('resize', this.boundOnWindowResize)
    }
  }

  /**
   * 窗口大小改变处理
   */
  onWindowResize() {
    const width = window.innerWidth
    const height = window.innerHeight

    console.log('窗口大小改变:', { width, height })

    this.camera.aspect = width / height
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(width, height)

    // 确保canvas样式正确
    this.renderer.domElement.style.width = '100vw'
    this.renderer.domElement.style.height = '100vh'

    // 更新标签管理器
    if (this.labelManager) {
      this.labelManager.onWindowResize()
    }
  }

  /**
   * 动画循环
   */
  animate() {
    if (!this.isAnimating) return

    this.animationId = requestAnimationFrame(this.animate.bind(this))

    if (this.controls) {
      this.controls.update()
    }

    this.renderer.render(this.scene, this.camera)

    // 渲染标签
    if (this.labelManager) {
      this.labelManager.render()
    }

    // 渲染CSS3D标签
    if (this.css3DLabelManager) {
      this.css3DLabelManager.render()
    }
  }

  /**
   * 开始动画循环
   */
  startAnimation() {
    if (!this.isAnimating) {
      this.isAnimating = true
      this.animate()
      console.log('动画循环已开始')
    }
  }

  /**
   * 停止动画循环
   */
  stopAnimation() {
    this.isAnimating = false
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
      console.log('动画循环已停止')
    }
  }

  /**
   * 初始化 GUI 控制器
   */
  initGUI(options = {}) {
    if (this.guiType === 'extended') {
      this.gui = new GUIControllerExtended(this, options)
    } else {
      this.gui = new GUIController(this)
    }
  }

  /**
   * 设置 GUI 类型并重新初始化
   */
  setGUIType(type, options = {}) {
    if (this.gui) {
      this.gui.dispose()
      this.gui = null
    }

    this.guiType = type
    if (this.enableGUI) {
      this.initGUI(options)
    }
  }

  /**
   * 销毁场景
   */
  dispose() {
    // 停止动画循环
    this.stopAnimation()



    if (this.gui) {
      this.gui.dispose()
    }

    if (this.labelManager) {
      this.labelManager.dispose()
    }

    if (this.css3DLabelManager) {
      this.css3DLabelManager.dispose()
    }

    if (this.floorManager) {
      this.floorManager.dispose()
    }

    if (this.dynamicSkybox) {
      this.dynamicSkybox.dispose()
    }

    if (this.controls) {
      this.controls.dispose()
    }

    if (this.renderer) {
      this.renderer.dispose()
      if (this.container && this.renderer.domElement.parentNode === this.container) {
        this.container.removeChild(this.renderer.domElement)
      }
    }

    // 移除事件监听器
    this.removeEventListeners()

    console.log('ThreeScene 已销毁')
  }

  /**
   * 获取场景对象
   */
  getScene() {
    return this.scene
  }

  /**
   * 获取相机对象
   */
  getCamera() {
    return this.camera
  }

  /**
   * 获取渲染器对象
   */
  getRenderer() {
    return this.renderer
  }

  /**
   * 获取控制器对象
   */
  getControls() {
    return this.controls
  }

  /**
   * 获取加载的模型
   */
  getModel() {
    return this.model
  }

  /**
   * 获取标签管理器
   */
  getLabelManager() {
    return this.labelManager
  }

  /**
   * 获取 GUI 控制器
   */
  getGUI() {
    return this.gui
  }

  /**
   * 获取楼层管理器
   */
  getFloorManager() {
    return this.floorManager
  }

  /**
   * 切换 GUI 显示/隐藏
   */
  toggleGUI() {
    if (this.gui) {
      this.gui.toggle()
    }
  }

  /**
   * 创建测试立方体（用于测试场景）
   */
  createTestCube() {
    // 创建立方体几何体
    const geometry = new THREE.BoxGeometry(20, 20, 20)

    // 创建材质
    const material = new THREE.MeshLambertMaterial({
      color: 0x00bcd4,
      transparent: true,
      opacity: 0.8
    })

    // 创建网格
    const cube = new THREE.Mesh(geometry, material)
    cube.position.set(0, 10, 0)
    cube.castShadow = true
    cube.receiveShadow = true

    // 添加到场景
    this.scene.add(cube)
    this.model = cube

    // 创建地面
    const groundGeometry = new THREE.PlaneGeometry(100, 100)
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 })
    const ground = new THREE.Mesh(groundGeometry, groundMaterial)
    ground.rotation.x = -Math.PI / 2
    ground.receiveShadow = true
    this.scene.add(ground)

    // 添加一些装饰立方体
    for (let i = 0; i < 5; i++) {
      const decorGeometry = new THREE.BoxGeometry(5, 15, 5)
      const decorMaterial = new THREE.MeshLambertMaterial({
        color: Math.random() * 0xffffff
      })
      const decorCube = new THREE.Mesh(decorGeometry, decorMaterial)
      decorCube.position.set(
        (Math.random() - 0.5) * 60,
        7.5,
        (Math.random() - 0.5) * 60
      )
      decorCube.castShadow = true
      decorCube.receiveShadow = true
      this.scene.add(decorCube)
    }

    console.log('测试立方体场景创建完成')
    return Promise.resolve({ scene: cube })
  }
}
