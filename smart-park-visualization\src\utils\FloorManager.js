import * as THREE from 'three'
import viewConfig from '../config/angleofview.json'

/**
 * 楼层管理器
 * 负责管理楼宇模型的分层显示控制
 */
export class FloorManager {
  constructor(threeScene) {
    this.threeScene = threeScene
    this.scene = threeScene.scene
    this.camera = threeScene.camera
    this.model = null
    
    // 楼层配置
    this.floors = {
      'roof': { name: '楼顶', visible: true, objects: [] },
      'floor-2': { name: '2楼', visible: true, objects: [] },
      'floor-1': { name: '1楼', visible: true, objects: [] }
    }
    
    // 当前显示的楼层
    this.currentFloor = 'all' // 'all', 'roof', 'floor-2', 'floor-1'

    // 楼层识别关键词（根据实际模型结构调整）
    this.floorKeywords = {
      'roof': ['roof', '楼顶', '顶层', 'top', 'Roof', 'TOP', '屋顶'],
      'floor-2': ['floor-2', '2楼', '二楼', 'second', 'Floor-2', 'FLOOR-2', 'Floor_2', 'floor2'],
      'floor-1': ['floor-1', '1楼', '一楼', 'first', 'ground', 'Floor-1', 'FLOOR-1', 'Floor_1', 'floor1']
    }

    // 控制器限制管理
    this.originalLimits = null // 存储原始限制参数
    this.limitsDisabled = false // 限制是否已解除

    // 性能优化相关
    this.isSwitching = false // 切换状态锁定
    this.switchingPromise = null // 当前切换的Promise
    this.currentAnimationId = null // 当前动画的ID
    this.lastSwitchTime = 0 // 上次切换时间
    this.switchDebounceDelay = 500 // 防抖延迟（毫秒）- 增强防抖
    this.meshCache = new Map() // 网格对象缓存

    // 状态恢复相关
    this.animationTimeout = null // 动画超时定时器
    this.maxAnimationDuration = 3000 // 最大动画持续时间（毫秒）
    this.originalControlsEnabled = true // 原始控制器启用状态
  }

  /**
   * 保存当前控制器限制参数
   */
  saveControllerLimits() {
    if (!this.threeScene.controls) return
    
    this.originalLimits = {
      enablePan: this.threeScene.controls.enablePan,
      minDistance: this.threeScene.controls.minDistance,
      maxDistance: this.threeScene.controls.maxDistance,
      minAzimuthAngle: this.threeScene.controls.minAzimuthAngle,
      maxAzimuthAngle: this.threeScene.controls.maxAzimuthAngle,
      minPolarAngle: this.threeScene.controls.minPolarAngle,
      maxPolarAngle: this.threeScene.controls.maxPolarAngle
    }
    
    console.log('已保存原始控制器限制参数:', this.originalLimits)
  }

  /**
   * 检查是否应该保持控制器无限制状态
   * 在楼宇模型状态下应该保持无限制，提供自由视角体验
   */
  shouldKeepUnlimited() {
    const isInBuildingMode = this.threeScene.modelSwitchManager && 
                            this.threeScene.modelSwitchManager.currentModelType === 'building'
    return isInBuildingMode
  }

  /**
   * 设置完全无限制的控制器状态（永久生效）
   */
  disableControllerLimits() {
    if (!this.threeScene.controls) return

    // 设置完全无限制的控制器状态
    this.threeScene.controls.enablePan = true        // 启用平移
    this.threeScene.controls.enableZoom = true       // 启用缩放
    this.threeScene.controls.enableRotate = true     // 启用旋转
    this.threeScene.controls.screenSpacePanning = true // 启用屏幕空间平移

    // 完全移除所有距离限制
    this.threeScene.controls.minDistance = 0         // 完全无限制的最小距离
    this.threeScene.controls.maxDistance = Infinity  // 完全无限制的最大距离

    // 完全移除所有角度限制
    this.threeScene.controls.minAzimuthAngle = -Infinity  // 完全无限制的水平旋转
    this.threeScene.controls.maxAzimuthAngle = Infinity   // 完全无限制的水平旋转
    this.threeScene.controls.minPolarAngle = 0            // 完全无限制的垂直旋转
    this.threeScene.controls.maxPolarAngle = Math.PI      // 完全无限制的垂直旋转

    // 设置控制速度
    this.threeScene.controls.panSpeed = 2.0        // 增加平移速度
    this.threeScene.controls.rotateSpeed = 1.0     // 旋转速度
    this.threeScene.controls.zoomSpeed = 1.0       // 缩放速度

    this.limitsDisabled = true
    console.log('✅ 已设置完全无限制的控制器状态，享受自由的视角控制！')
  }

  /**
   * 恢复控制器限制（已禁用 - 永久保持无限制状态）
   * @param {boolean} forceRestore - 是否强制恢复，忽略楼宇模型状态检查
   */
  restoreControllerLimits(forceRestore = false) {
    // 完全禁用限制恢复功能，永久保持无限制状态
    console.log('控制器限制恢复已永久禁用，保持完全自由的视角控制')
    return

    // 以下代码已被完全禁用，确保永远不会重新应用任何限制
    /*
    // 检查是否应该保持无限制状态，除非强制恢复
    if (!forceRestore && this.shouldKeepUnlimited()) {
      console.log('楼宇模型状态：跳过恢复控制器限制，保持自由视角')
      return
    }

    if (!this.threeScene.controls || !this.originalLimits || !this.limitsDisabled) return

    // 恢复原始限制参数
    this.threeScene.controls.enablePan = this.originalLimits.enablePan
    this.threeScene.controls.minDistance = this.originalLimits.minDistance
    this.threeScene.controls.maxDistance = this.originalLimits.maxDistance
    this.threeScene.controls.minAzimuthAngle = this.originalLimits.minAzimuthAngle
    this.threeScene.controls.maxAzimuthAngle = this.originalLimits.maxAzimuthAngle
    this.threeScene.controls.minPolarAngle = this.originalLimits.minPolarAngle
    this.threeScene.controls.maxPolarAngle = this.originalLimits.maxPolarAngle

    this.limitsDisabled = false

    if (forceRestore) {
      console.log('强制恢复控制器限制完成')
    } else {
      console.log('楼层切换完成：已恢复控制器限制')
    }
    */
  }
  
  /**
   * 设置要管理的模型
   * @param {THREE.Object3D} model - 楼宇模型
   */
  setModel(model) {
    this.model = model
    if (model) {
      this.analyzeModelStructure()
    }
  }

  /**
   * 设置整体模型（用于楼层控制）
   * @param {THREE.Object3D} overallModel - 整体模型
   */
  setOverallModel(overallModel) {
    console.log('设置整体模型用于楼层控制:', overallModel)

    // 清理之前的缓存和状态
    this.clearCache()
    this.cancelCurrentAnimation()
    this.isSwitching = false

    this.model = overallModel

    // 重置楼层状态到默认值
    this.currentFloor = 'all'
    console.log('楼层状态已重置为: all（全部显示）')

    if (overallModel) {
      this.analyzeModelStructure()

      // 重置楼层可见性，使用优化的方法
      this.applyCuttingPlaneEffectOptimized('all')
      console.log('楼层可见性已重置为全部显示')

      // 输出模型结构信息用于调试
      this.debugModelStructure()
    } else {
      console.warn('整体模型为空，无法进行楼层分析')
    }
  }

  /**
   * 清理缓存和重置状态
   */
  clearCache() {
    this.meshCache.clear()
    this.modelBounds = null
    console.log('楼层管理器缓存已清理')
  }
  
  /**
   * 分析模型结构（简化版，用于分层剖切）
   */
  analyzeModelStructure() {
    if (!this.model) return

    console.log('开始分析楼宇模型结构（分层剖切模式）...')
    console.log('模型对象:', this.model)

    // 获取模型基本信息
    const box = new THREE.Box3().setFromObject(this.model)
    const size = box.getSize(new THREE.Vector3())

    console.log('模型边界盒:', {
      min: { x: box.min.x.toFixed(2), y: box.min.y.toFixed(2), z: box.min.z.toFixed(2) },
      max: { x: box.max.x.toFixed(2), y: box.max.y.toFixed(2), z: box.max.z.toFixed(2) },
      size: { x: size.x.toFixed(2), y: size.y.toFixed(2), z: size.z.toFixed(2) }
    })

    // 统计网格对象数量
    let meshCount = 0
    this.model.traverse((child) => {
      if (child.isMesh) {
        meshCount++
      }
    })

    console.log(`模型包含 ${meshCount} 个网格对象`)
    console.log('分层剖切模式已准备就绪')
  }

  /**
   * 输出完整的模型结构
   */
  logModelStructure() {
    console.log('=== 模型结构分析 ===')
    const logObject = (obj, depth = 0) => {
      const indent = '  '.repeat(depth)
      console.log(`${indent}${obj.name || '未命名'} (${obj.type}) - 子对象数: ${obj.children.length}`)
      if (obj.position) {
        console.log(`${indent}  位置: x=${obj.position.x.toFixed(2)}, y=${obj.position.y.toFixed(2)}, z=${obj.position.z.toFixed(2)}`)
      }
      if (depth < 3) { // 限制深度避免输出过多
        obj.children.forEach(child => logObject(child, depth + 1))
      }
    }
    logObject(this.model)
    console.log('=== 结构分析结束 ===')
  }

  /**
   * 智能楼层识别
   * 基于模型的层级结构和命名模式进行识别
   */
  smartFloorIdentification() {
    console.log('开始智能楼层识别...')

    // 策略1: 如果模型有明确的楼层分组，直接使用
    const directFloorGroups = this.findDirectFloorGroups()
    if (directFloorGroups.length > 0) {
      console.log('找到直接楼层分组:', directFloorGroups.map(g => g.group.name))
      return
    }

    // 策略2: 如果没有直接分组，尝试根据高度自动分组
    console.log('未找到直接楼层分组，尝试高度分组...')
    this.autoGroupByHeight()

    // 策略3: 如果高度分组也失败，使用简单分组
    const totalObjects = Object.values(this.floors).reduce((sum, floor) => sum + floor.objects.length, 0)
    if (totalObjects === 0) {
      console.log('高度分组失败，使用简单分组策略...')
      this.simpleFloorGrouping()
    }
  }

  /**
   * 查找直接的楼层分组
   */
  findDirectFloorGroups() {
    const floorGroups = []

    this.model.traverse((child) => {
      if (child.isGroup && child.children.length > 0) {
        const name = child.name.toLowerCase()
        // 检查是否是楼层分组
        for (const [floorKey, keywords] of Object.entries(this.floorKeywords)) {
          for (const keyword of keywords) {
            if (name.includes(keyword.toLowerCase())) {
              floorGroups.push({ group: child, floorType: floorKey })
              // 将整个分组添加到对应楼层
              this.floors[floorKey].objects.push(child)
              console.log(`找到楼层分组: ${child.name} -> ${this.floors[floorKey].name}`)
              break
            }
          }
        }
      }
    })

    return floorGroups
  }

  /**
   * 根据高度自动分组
   */
  autoGroupByHeight() {
    console.log('根据高度自动分组楼层...')

    const meshes = []
    this.model.traverse((child) => {
      if (child.isMesh) {
        const worldPos = new THREE.Vector3()
        child.getWorldPosition(worldPos)
        meshes.push({ mesh: child, y: worldPos.y })
      }
    })

    if (meshes.length === 0) return

    // 按高度排序
    meshes.sort((a, b) => a.y - b.y)

    // 计算高度分布
    const minY = meshes[0].y
    const maxY = meshes[meshes.length - 1].y
    const range = maxY - minY

    console.log(`高度范围: ${minY.toFixed(2)} 到 ${maxY.toFixed(2)}, 总范围: ${range.toFixed(2)}`)

    // 如果高度范围太小，说明可能是平面模型
    if (range < 5) {
      console.log('模型高度范围较小，可能是平面模型，将所有对象归为1楼')
      meshes.forEach(item => {
        this.floors['floor-1'].objects.push(item.mesh)
      })
      return
    }

    // 根据高度分成三层
    const thirdRange = range / 3
    meshes.forEach(item => {
      const relativeY = item.y - minY
      if (relativeY > thirdRange * 2) {
        this.floors['roof'].objects.push(item.mesh)
      } else if (relativeY > thirdRange) {
        this.floors['floor-2'].objects.push(item.mesh)
      } else {
        this.floors['floor-1'].objects.push(item.mesh)
      }
    })

    console.log('自动高度分组完成')
  }

  /**
   * 简单楼层分组策略
   * 将所有网格对象平均分配到三个楼层
   */
  simpleFloorGrouping() {
    console.log('执行简单楼层分组策略...')

    const allMeshes = []
    this.model.traverse((child) => {
      if (child.isMesh) {
        allMeshes.push(child)
      }
    })

    if (allMeshes.length === 0) {
      console.warn('模型中没有找到网格对象')
      return
    }

    console.log(`找到 ${allMeshes.length} 个网格对象，进行简单分组`)

    // 将网格对象平均分配到三个楼层
    const perFloor = Math.ceil(allMeshes.length / 3)

    allMeshes.forEach((mesh, index) => {
      if (index < perFloor) {
        this.floors['floor-1'].objects.push(mesh)
      } else if (index < perFloor * 2) {
        this.floors['floor-2'].objects.push(mesh)
      } else {
        this.floors['roof'].objects.push(mesh)
      }
    })

    console.log('简单分组完成:')
    console.log(`1楼: ${this.floors['floor-1'].objects.length} 个对象`)
    console.log(`2楼: ${this.floors['floor-2'].objects.length} 个对象`)
    console.log(`楼顶: ${this.floors['roof'].objects.length} 个对象`)
  }
  
  /**
   * 识别对象属于哪个楼层
   * @param {THREE.Object3D} object - 要识别的对象
   * @returns {string|null} - 楼层类型或null
   */
  identifyFloorType(object) {
    const name = object.name.toLowerCase()
    const parentName = object.parent ? object.parent.name.toLowerCase() : ''
    const originalName = object.name // 保留原始名称用于精确匹配
    const originalParentName = object.parent ? object.parent.name : ''

    // 检查对象名称和父对象名称（包括大小写敏感的匹配）
    for (const [floorKey, keywords] of Object.entries(this.floorKeywords)) {
      for (const keyword of keywords) {
        const lowerKeyword = keyword.toLowerCase()
        // 检查小写匹配
        if (name.includes(lowerKeyword) || parentName.includes(lowerKeyword)) {
          return floorKey
        }
        // 检查精确匹配（大小写敏感）
        if (originalName.includes(keyword) || originalParentName.includes(keyword)) {
          return floorKey
        }
      }
    }

    // 如果没有明确的关键词，尝试根据位置判断
    return this.identifyFloorByPosition(object)
  }
  
  /**
   * 根据对象位置识别楼层
   * @param {THREE.Object3D} object - 要识别的对象
   * @returns {string|null} - 楼层类型或null
   */
  identifyFloorByPosition(object) {
    // 获取对象的世界位置
    const worldPosition = new THREE.Vector3()
    object.getWorldPosition(worldPosition)

    const y = worldPosition.y

    // 根据Y坐标判断楼层（调整阈值以适应实际模型）
    // 这些值需要根据实际模型的尺寸进行调整
    if (y > 15) {
      return 'roof' // 楼顶
    } else if (y > 5) {
      return 'floor-2' // 2楼
    } else if (y >= -5) {
      return 'floor-1' // 1楼
    }

    return null
  }
  
  /**
   * 输出分析结果
   */
  logAnalysisResult() {
    console.log('=== 楼层分析结果 ===')
    let totalObjects = 0
    Object.entries(this.floors).forEach(([floorKey, floor]) => {
      console.log(`${floor.name}: ${floor.objects.length} 个对象`)
      totalObjects += floor.objects.length
      floor.objects.forEach(obj => {
        console.log(`  - ${obj.name || '未命名对象'} (类型: ${obj.type})`)
      })
    })

    console.log(`总计识别到 ${totalObjects} 个楼层对象`)

    if (totalObjects === 0) {
      console.warn('⚠️ 没有识别到任何楼层对象！')
      console.log('可能的原因：')
      console.log('1. 模型命名不符合预期的关键词')
      console.log('2. 模型结构与预期不符')
      console.log('3. 需要调整楼层识别策略')

      // 提供备用方案：将所有网格对象都归为1楼
      this.fallbackFloorAssignment()
    }
    console.log('=== 分析结束 ===')
  }

  /**
   * 备用楼层分配方案
   */
  fallbackFloorAssignment() {
    console.log('执行备用楼层分配方案...')

    let meshCount = 0
    this.model.traverse((child) => {
      if (child.isMesh) {
        this.floors['floor-1'].objects.push(child)
        meshCount++
      }
    })

    console.log(`备用方案：将 ${meshCount} 个网格对象分配到1楼`)
  }
  
  /**
   * 设置楼层显示状态
   * @param {string} floorKey - 楼层键值
   * @param {boolean} visible - 是否显示
   */
  setFloorVisibility(floorKey, visible) {
    if (!this.floors[floorKey]) {
      console.warn(`未知的楼层: ${floorKey}`)
      return
    }
    
    this.floors[floorKey].visible = visible
    this.floors[floorKey].objects.forEach(obj => {
      obj.visible = visible
    })
    
    console.log(`${this.floors[floorKey].name} ${visible ? '显示' : '隐藏'}`)
  }
  
  /**
   * 切换到指定楼层（分层剖切效果）- 带增强防抖和状态锁定
   * @param {string} targetFloor - 目标楼层 ('all', 'roof', 'floor-2', 'floor-1')
   */
  switchToFloor(targetFloor) {
    console.log(`=== 楼层切换请求: ${targetFloor} ===`)

    if (!this.model) {
      console.error('模型未设置，无法进行楼层切换')
      this.forceResetSwitchingState()
      return
    }

    // 增强防抖检查
    const currentTime = Date.now()
    if (currentTime - this.lastSwitchTime < this.switchDebounceDelay) {
      console.log(`楼层切换过于频繁，忽略请求（距离上次切换${currentTime - this.lastSwitchTime}ms）`)
      return
    }

    // 如果目标楼层与当前楼层相同，直接返回
    if (targetFloor === this.currentFloor) {
      console.log(`已经在目标楼层 ${targetFloor}，无需切换`)
      this.forceResetSwitchingState() // 确保状态正确
      return
    }

    // 检查是否正在切换中
    if (this.isSwitching) {
      console.log('楼层切换正在进行中，强制重置状态后重试')
      this.forceResetSwitchingState()
      // 短暂延迟后重试
      setTimeout(() => {
        this.switchToFloor(targetFloor)
      }, 100)
      return
    }

    // 设置切换状态锁定
    this.isSwitching = true
    this.lastSwitchTime = currentTime

    console.log(`开始切换楼层: ${this.currentFloor} → ${targetFloor}`)

    try {
      this._performFloorSwitch(targetFloor)
    } catch (error) {
      console.error('楼层切换失败:', error)
      this.forceResetSwitchingState()
    }
    // 注意：不在这里释放isSwitching，而是在动画完成后释放
  }

  /**
   * 执行楼层切换的具体逻辑
   * @param {string} targetFloor - 目标楼层
   */
  _performFloorSwitch(targetFloor) {
    // 取消当前正在进行的动画
    this.cancelCurrentAnimation()

    // 使用优化的分层剖切效果
    this.applyCuttingPlaneEffectOptimized(targetFloor)

    // 根据楼层切换相机视角
    this.applyCameraViewpoint(targetFloor)

    this.currentFloor = targetFloor
    console.log(`楼层切换完成: ${targetFloor}`)

    // 通知标签管理器更新显示状态
    if (this.threeScene.labelManager) {
      this.threeScene.labelManager.updateLabelsVisibility()
    }


  }

  /**
   * 强制重置切换状态（用于异常恢复）
   */
  forceResetSwitchingState() {
    console.log('强制重置楼层切换状态')

    // 取消所有动画和定时器
    this.cancelCurrentAnimation()
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout)
      this.animationTimeout = null
    }

    // 重置状态标志
    this.isSwitching = false

    // 恢复控制器状态
    if (this.threeScene && this.threeScene.controls) {
      this.threeScene.controls.enabled = this.originalControlsEnabled
      this.threeScene.controls.update()
    }

    console.log('楼层切换状态已强制重置')
  }

  /**
   * 取消当前正在进行的动画（增强版）
   */
  cancelCurrentAnimation() {
    if (this.currentAnimationId) {
      cancelAnimationFrame(this.currentAnimationId)
      this.currentAnimationId = null
      console.log('已取消当前楼层切换动画')
    }

    // 清理超时定时器
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout)
      this.animationTimeout = null
      console.log('动画超时定时器已清理')
    }

    // 确保控制器状态正确
    if (this.threeScene && this.threeScene.controls) {
      this.threeScene.controls.enabled = this.originalControlsEnabled
    }
  }

  /**
   * 优化的分层剖切效果（基于名称识别楼层）
   * @param {string} targetFloor - 目标楼层
   */
  applyCuttingPlaneEffectOptimized(targetFloor) {
    if (!this.model) return

    console.log(`开始分层控制: ${targetFloor}`)

    // 统计信息
    const stats = {
      total: 0,
      visible: 0,
      hidden: 0,
      floor1: 0,
      floor2: 0,
      roof: 0
    }

    // 遍历模型中的所有对象
    this.model.traverse((child) => {
      if (child.isMesh) {
        stats.total++
        const floorType = this.identifyFloorTypeByName(child)

        // 统计楼层分布
        if (floorType === 'floor-1') stats.floor1++
        else if (floorType === 'floor-2') stats.floor2++
        else if (floorType === 'roof') stats.roof++

        let visible = true

        switch (targetFloor) {
          case 'all':
            // 完整建筑显示：显示所有楼层
            visible = true
            break
          case 'roof':
            // 楼顶显示：显示完整的建筑模型
            visible = true
            break
          case 'floor-1':
            // 1层分层显示：只显示1层的模型部分
            visible = (floorType === 'floor-1')
            break
          case 'floor-2':
            // 2层分层显示：显示1层和2层的模型部分
            visible = (floorType === 'floor-1' || floorType === 'floor-2')
            break
          default:
            visible = true
        }

        // 更新对象可见性
        if (child.visible !== visible) {
          child.visible = visible
        }

        // 统计可见性
        if (visible) {
          stats.visible++
        } else {
          stats.hidden++
        }
      }
    })

    // 输出统计信息
    console.log(`分层控制完成: ${targetFloor}`)
    console.log(`统计信息:`, {
      总对象数: stats.total,
      显示: stats.visible,
      隐藏: stats.hidden,
      一楼对象: stats.floor1,
      二楼对象: stats.floor2,
      楼顶对象: stats.roof
    })
  }

  /**
   * 基于名称识别楼层类型（优化版）
   * @param {THREE.Object3D} object - 要识别的对象
   * @returns {string} - 楼层类型
   */
  identifyFloorTypeByName(object) {
    const name = object.name.toLowerCase()
    const parentName = object.parent ? object.parent.name.toLowerCase() : ''
    const originalName = object.name // 保留原始名称用于精确匹配
    const originalParentName = object.parent ? object.parent.name : ''

    // 优先检查精确匹配（大小写敏感）
    if (originalName.includes('Floor-1') || originalParentName.includes('Floor-1') ||
        originalName.includes('1楼') || originalParentName.includes('1楼')) {
      return 'floor-1'
    }

    if (originalName.includes('Floor-2') || originalParentName.includes('Floor-2') ||
        originalName.includes('2楼') || originalParentName.includes('2楼')) {
      return 'floor-2'
    }

    // 检查小写匹配
    if (name.includes('floor-1') || parentName.includes('floor-1') ||
        name.includes('floor_1') || parentName.includes('floor_1') ||
        name.includes('一楼') || parentName.includes('一楼') ||
        name.includes('first') || parentName.includes('first') ||
        name.includes('ground') || parentName.includes('ground')) {
      return 'floor-1'
    }

    if (name.includes('floor-2') || parentName.includes('floor-2') ||
        name.includes('floor_2') || parentName.includes('floor_2') ||
        name.includes('二楼') || parentName.includes('二楼') ||
        name.includes('second') || parentName.includes('second')) {
      return 'floor-2'
    }

    if (name.includes('roof') || parentName.includes('roof') ||
        name.includes('楼顶') || parentName.includes('楼顶') ||
        name.includes('顶层') || parentName.includes('顶层') ||
        name.includes('top') || parentName.includes('top')) {
      return 'roof'
    }

    // 如果没有明确的楼层标识，默认为1楼
    return 'floor-1'
  }

  /**
   * 调试模型结构（输出对象名称和楼层识别结果）
   */
  debugModelStructure() {
    if (!this.model) return

    console.log('=== 模型结构调试信息 ===')
    const floorCounts = { 'floor-1': 0, 'floor-2': 0, 'roof': 0 }

    this.model.traverse((child) => {
      if (child.isMesh && child.name) {
        const floorType = this.identifyFloorTypeByName(child)
        floorCounts[floorType]++
        console.log(`对象: "${child.name}" -> 楼层: ${floorType}`)
      }
    })

    console.log('楼层统计:', floorCounts)
    console.log('=== 调试信息结束 ===')
  }

  /**
   * 构建网格对象缓存
   */
  buildMeshCache() {
    console.log('构建网格对象缓存...')
    this.meshCache.clear()

    let meshCount = 0
    this.model.traverse((child) => {
      if (child.isMesh) {
        const worldPos = new THREE.Vector3()
        child.getWorldPosition(worldPos)

        this.meshCache.set(`mesh_${meshCount}`, {
          object: child,
          worldPosition: worldPos.clone()
        })
        meshCount++
      }
    })

    console.log(`网格缓存构建完成，共 ${meshCount} 个对象`)
  }

  /**
   * 计算模型边界信息
   */
  calculateModelBounds() {
    const box = new THREE.Box3().setFromObject(this.model)
    const size = box.getSize(new THREE.Vector3())
    const minY = box.min.y
    const maxY = box.max.y
    const height = size.y

    // 定义分层高度（将建筑分成三层）
    const floorHeight = height / 3
    const floor1Top = minY + floorHeight
    const floor2Top = minY + floorHeight * 2

    this.modelBounds = {
      minY,
      maxY,
      height,
      floorHeight,
      floor1Top,
      floor2Top
    }

    console.log('模型边界信息已缓存:', this.modelBounds)
  }

  /**
   * 应用分层剖切效果
   * @param {string} targetFloor - 目标楼层
   */
  applyCuttingPlaneEffect(targetFloor) {
    if (!this.model) return

    // 获取模型的边界盒
    const box = new THREE.Box3().setFromObject(this.model)
    const size = box.getSize(new THREE.Vector3())
    const minY = box.min.y
    const maxY = box.max.y
    const height = size.y

    console.log(`模型高度范围: ${minY.toFixed(2)} 到 ${maxY.toFixed(2)}, 总高度: ${height.toFixed(2)}`)

    // 定义分层高度（将建筑分成三层）
    const floorHeight = height / 3
    const floor1Top = minY + floorHeight
    const floor2Top = minY + floorHeight * 2

    console.log(`楼层分界线: 1楼顶部=${floor1Top.toFixed(2)}, 2楼顶部=${floor2Top.toFixed(2)}`)

    // 遍历所有网格对象，根据目标楼层设置可见性
    this.model.traverse((child) => {
      if (child.isMesh) {
        const worldPos = new THREE.Vector3()
        child.getWorldPosition(worldPos)
        const objectY = worldPos.y

        let visible = true

        switch (targetFloor) {
          case 'all':
            // 显示所有
            visible = true
            break

          case 'roof':
            // 显示所有（楼顶视图）
            visible = true
            break

          case 'floor-2':
            // 隐藏楼顶部分，显示2楼及以下
            visible = objectY <= floor2Top
            break

          case 'floor-1':
            // 隐藏2楼及以上，只显示1楼
            visible = objectY <= floor1Top
            break

          default:
            visible = true
        }

        child.visible = visible
      }
    })

    console.log(`分层剖切效果已应用: ${targetFloor}`)
  }

  /**
   * 应用相机视角
   * @param {string} targetFloor - 目标楼层
   */
  applyCameraViewpoint(targetFloor) {
    if (!this.threeScene || !this.threeScene.controls) return

    let viewpointConfig = null

    // 根据楼层选择对应的视角配置
    switch (targetFloor) {
      case 'floor-1':
        // 1楼使用专用视角配置
        viewpointConfig = viewConfig.floorViewpoints['floor-1']
        console.log(`应用1楼视角: ${targetFloor}`)
        break
      case 'floor-2':
        // 2楼使用专用视角配置
        viewpointConfig = viewConfig.floorViewpoints['floor-2']
        console.log(`应用2楼视角: ${targetFloor}`)
        break
      case 'all':
      case 'roof':
        // 默认使用整体场景视角
        viewpointConfig = viewConfig.defaultViewpoint
        console.log(`应用默认视角: ${targetFloor}`)
        break
      default:
        console.log(`未知楼层，不切换视角: ${targetFloor}`)
        return
    }

    if (viewpointConfig) {
      // 创建目标相机位置和目标点
      const targetPosition = new THREE.Vector3(
        viewpointConfig.camera.position.x,
        viewpointConfig.camera.position.y,
        viewpointConfig.camera.position.z
      )
      const targetLookAt = new THREE.Vector3(
        viewpointConfig.controls.target.x,
        viewpointConfig.controls.target.y,
        viewpointConfig.controls.target.z
      )

      // 创建目标相机旋转角度
      const targetRotation = new THREE.Euler(
        viewpointConfig.camera.rotation.x,
        viewpointConfig.camera.rotation.y,
        viewpointConfig.camera.rotation.z
      )

      // 应用相机动画（包含旋转角度）
      this.animateCameraTo(targetPosition, targetLookAt, targetRotation, viewpointConfig.controls)
    }
  }

  /**
   * 相机动画过渡（增强版，支持超时保护和状态管理）
   * @param {THREE.Vector3} targetPosition - 目标位置
   * @param {THREE.Vector3} targetLookAt - 目标朝向
   * @param {THREE.Euler} targetRotation - 目标旋转角度
   * @param {Object} controlsConfig - 控制器配置参数
   * @param {number} duration - 动画持续时间（毫秒）
   */
  animateCameraTo(targetPosition, targetLookAt, targetRotation, controlsConfig, duration = 1000) {
    if (!this.camera || !this.threeScene.controls) {
      console.error('相机或控制器未初始化，无法执行动画')
      this.forceResetSwitchingState()
      return
    }

    // 取消之前的动画和定时器
    this.cancelCurrentAnimation()
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout)
      this.animationTimeout = null
    }

    const startPosition = this.camera.position.clone()
    const startTarget = this.threeScene.controls.target.clone()

    // 保存原始控制器状态
    this.originalControlsEnabled = this.threeScene.controls.enabled
    this.threeScene.controls.enabled = false

    const startTime = Date.now()

    // 设置超时保护
    this.animationTimeout = setTimeout(() => {
      console.warn('动画超时，强制完成')
      this.forceCompleteAnimation(targetPosition, targetLookAt, targetRotation)
    }, this.maxAnimationDuration)

    const animate = () => {
      // 检查动画是否被取消
      if (!this.currentAnimationId) {
        console.log('动画被取消，恢复控制器状态')
        this.threeScene.controls.enabled = this.originalControlsEnabled
        this.isSwitching = false
        if (this.animationTimeout) {
          clearTimeout(this.animationTimeout)
          this.animationTimeout = null
        }
        return
      }

      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      // 使用缓动函数
      const easeProgress = this.easeInOutCubic(progress)

      // 插值相机位置
      this.camera.position.lerpVectors(startPosition, targetPosition, easeProgress)
      this.threeScene.controls.target.lerpVectors(startTarget, targetLookAt, easeProgress)

      // 在动画过程中，让相机直接看向目标点，避免旋转插值冲突
      this.camera.lookAt(this.threeScene.controls.target)

      if (progress < 1) {
        this.currentAnimationId = requestAnimationFrame(animate)
      } else {
        // 动画正常完成
        this.completeAnimation(targetPosition, targetLookAt, targetRotation)
      }
    }

    // 开始动画
    this.currentAnimationId = requestAnimationFrame(animate)
  }

  /**
   * 完成动画（正常完成）
   */
  completeAnimation(targetPosition, targetLookAt, targetRotation) {
    console.log('动画正常完成，清理状态')

    // 清理动画状态
    this.currentAnimationId = null
    this.isSwitching = false

    // 清理超时定时器
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout)
      this.animationTimeout = null
    }

    // 精确设置最终状态
    this.camera.position.copy(targetPosition)
    this.threeScene.controls.target.copy(targetLookAt)
    this.camera.rotation.set(targetRotation.x, targetRotation.y, targetRotation.z)

    // 重新启用控制器
    this.threeScene.controls.enabled = this.originalControlsEnabled
    this.threeScene.controls.update()

    // 检查是否在楼宇模型状态
    const isInBuildingMode = this.threeScene.modelSwitchManager &&
                            this.threeScene.modelSwitchManager.currentModelType === 'building'

    // 只有在非楼宇模型状态下才自动恢复限制
    if (!isInBuildingMode) {
      setTimeout(() => {
        this.restoreControllerLimits()
      }, 2000)
    } else {
      console.log('楼宇模型状态：保持控制器无限制状态')
    }

    console.log('楼层视角切换完成，状态锁定已解除')
  }

  /**
   * 强制完成动画（超时或异常情况）
   */
  forceCompleteAnimation(targetPosition, targetLookAt, targetRotation) {
    console.warn('强制完成动画')

    // 取消当前动画
    if (this.currentAnimationId) {
      cancelAnimationFrame(this.currentAnimationId)
      this.currentAnimationId = null
    }

    // 调用正常完成流程
    this.completeAnimation(targetPosition, targetLookAt, targetRotation)
  }

  /**
   * 缓动函数
   * @param {number} t - 时间进度 (0-1)
   * @returns {number} - 缓动后的进度
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
  }
  
  /**
   * 获取当前楼层
   * @returns {string} - 当前楼层
   */
  getCurrentFloor() {
    return this.currentFloor
  }
  
  /**
   * 获取楼层列表
   * @returns {Object} - 楼层配置对象
   */
  getFloors() {
    return this.floors
  }
  
  /**
   * 获取楼层选项（用于GUI下拉框）
   * @returns {Object} - 楼层选项对象
   */
  getFloorOptions() {
    const options = { '全部显示': 'all' }
    Object.entries(this.floors).forEach(([key, floor]) => {
      options[floor.name] = key
    })
    return options
  }
  
  /**
   * 重新分析模型结构
   */
  refresh() {
    if (this.model) {
      this.analyzeModelStructure()
    }
  }
  
  /**
   * 销毁管理器
   */
  dispose() {
    // 销毁时确保恢复控制器限制
    this.restoreControllerLimits()
    
    this.model = null
    Object.keys(this.floors).forEach(floorKey => {
      this.floors[floorKey].objects = []
    })
    
    // 清理限制管理相关属性
    this.originalLimits = null
    this.limitsDisabled = false
  }
}
